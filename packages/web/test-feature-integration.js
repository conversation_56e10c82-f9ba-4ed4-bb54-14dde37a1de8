#!/usr/bin/env node

/**
 * Test script for Feature Request Integration
 * Tests the complete workflow from web API to automated code generation
 *
 * Usage:
 *   node test-feature-integration.js
 *   node test-feature-integration.js --stream  # Test streaming API
 */

const { join } = require('node:path')
require('dotenv').config({ path: join(__dirname, '../remote-agent/.env') })

const TEST_FEATURE_REQUESTS = [
  {
    description: "Add a new API endpoint for user profile management that allows users to update their profile information including name, email, and avatar",
    expectedTools: ['github-analyze-issue', 'search-keywords', 'read-file', 'str-replace-editor'],
    expectedModifications: 1
  },
  {
    description: "Implement a simple logging utility that can write messages to both console and file with different log levels (info, warn, error)",
    expectedTools: ['list-directory', 'read-file', 'write-file'],
    expectedModifications: 1
  }
]

async function testFeatureRequestAPI(testCase, useStream = false) {
  console.log(`\n🚀 Testing Feature Request ${useStream ? 'Stream ' : ''}API`)
  console.log(`📝 Description: ${testCase.description.substring(0, 100)}...`)

  try {
    const endpoint = useStream ? '/api/feature-request/stream' : '/api/feature-request'
    const baseUrl = process.env.TEST_BASE_URL || 'http://localhost:3000'
    const url = `${baseUrl}${endpoint}`

    console.log(`🔗 Testing endpoint: ${url}`)

    const startTime = Date.now()
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        description: testCase.description,
        issueNumber: null
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    if (useStream) {
      return await handleStreamResponse(response, testCase, startTime)
    } else {
      return await handleJsonResponse(response, testCase, startTime)
    }

  } catch (error) {
    console.error('❌ API Test failed:', error.message)
    return {
      success: false,
      error: error.message,
      codeModifications: 0
    }
  }
}

async function handleJsonResponse(response, testCase, startTime) {
  const result = await response.json()
  const executionTime = Date.now() - startTime

  console.log(`\n📊 API Response Results:`)
  console.log(`✅ Success: ${result.success}`)
  console.log(`🔄 Rounds: ${result.totalRounds || 'N/A'}`)
  console.log(`🛠️ Tools Used: ${result.toolResults?.length || 0}`)
  console.log(`💻 Code Modifications: ${result.codeModifications}`)
  console.log(`⏱️ Execution Time: ${executionTime}ms`)

  if (result.summary) {
    console.log(`📄 Summary: ${result.summary.substring(0, 200)}...`)
  }

  if (result.error) {
    console.log(`❌ Error: ${result.error}`)
  }

  return {
    success: result.success,
    codeModifications: result.codeModifications,
    executionTime,
    error: result.error
  }
}

async function handleStreamResponse(response, testCase, startTime) {
  console.log(`\n📡 Receiving streaming updates...`)
  
  const reader = response.body?.getReader()
  const decoder = new TextDecoder()
  
  if (!reader) {
    throw new Error('Failed to get response reader')
  }

  let finalResult = null
  const progressSteps = []

  while (true) {
    const { done, value } = await reader.read()
    
    if (done) break

    const chunk = decoder.decode(value)
    const lines = chunk.split('\n')

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        try {
          const data = JSON.parse(line.slice(6))
          
          if (data.type === 'progress') {
            console.log(`📍 ${data.step}: ${data.status}${data.details ? ` - ${data.details}` : ''}`)
            progressSteps.push(data)
          } else if (data.type === 'result') {
            finalResult = data.result
            console.log(`\n🎉 Final Result Received`)
          } else if (data.type === 'error') {
            throw new Error(data.error)
          }
        } catch (parseError) {
          console.warn('⚠️ Failed to parse SSE data:', parseError.message)
        }
      }
    }
  }

  const executionTime = Date.now() - startTime

  if (finalResult) {
    console.log(`\n📊 Stream Results:`)
    console.log(`✅ Success: ${finalResult.success}`)
    console.log(`💻 Code Modifications: ${finalResult.codeModifications}`)
    console.log(`🔧 Tools Used: ${finalResult.toolsUsed?.join(', ') || 'N/A'}`)
    console.log(`⏱️ Total Time: ${executionTime}ms`)
    console.log(`📋 Progress Steps: ${progressSteps.length}`)

    if (finalResult.summary) {
      console.log(`📄 Summary: ${finalResult.summary.substring(0, 200)}...`)
    }
  }

  return {
    success: finalResult?.success || false,
    codeModifications: finalResult?.codeModifications || 0,
    executionTime,
    progressSteps: progressSteps.length,
    error: finalResult?.error
  }
}

async function testServiceDirectly(testCase) {
  console.log(`\n🔧 Testing FeatureRequestService directly`)
  console.log(`📝 Description: ${testCase.description.substring(0, 100)}...`)

  try {
    // Import the service
    const { FeatureRequestService } = require('../remote-agent/dist/services/feature-request-service.js')

    const service = new FeatureRequestService({
      description: testCase.description,
      workspacePath: join(process.cwd(), '../'),
      verbose: true,
      maxToolRounds: 6,
      validateCodeChanges: true
    })

    const startTime = Date.now()
    const result = await service.implementFeature()
    const executionTime = Date.now() - startTime

    console.log(`\n📊 Service Results:`)
    console.log(`✅ Success: ${result.success}`)
    console.log(`💻 Code Modifications: ${result.codeModifications}`)
    console.log(`🔧 Tools Used: ${result.toolsUsed.join(', ')}`)
    console.log(`📋 Progress Steps: ${result.progressSteps.length}`)
    console.log(`⏱️ Execution Time: ${executionTime}ms`)

    if (result.summary) {
      console.log(`📄 Summary: ${result.summary.substring(0, 200)}...`)
    }

    return {
      success: result.success,
      codeModifications: result.codeModifications,
      executionTime,
      error: result.error
    }

  } catch (error) {
    console.error('❌ Service test failed:', error.message)
    return {
      success: false,
      error: error.message,
      codeModifications: 0
    }
  }
}

// Main test execution
if (require.main === module) {
  (async () => {
    const args = process.argv.slice(2)
    const useStream = args.includes('--stream')
    const testService = args.includes('--service')
    const testAll = args.includes('--all') || (!useStream && !testService)

    console.log('🚀 Starting Feature Request Integration Test Suite')
    console.log(`📋 Running ${TEST_FEATURE_REQUESTS.length} test case(s)...`)
    
    if (useStream) console.log('🌊 Using streaming API')
    if (testService) console.log('🔧 Testing service directly')
    if (testAll) console.log('🔄 Testing both API and service')

    const results = []

    for (let i = 0; i < TEST_FEATURE_REQUESTS.length; i++) {
      const testCase = TEST_FEATURE_REQUESTS[i]
      console.log(`\n${'='.repeat(80)}`)
      console.log(`📋 Test Case ${i + 1}/${TEST_FEATURE_REQUESTS.length}`)

      try {
        if (testAll || !testService) {
          // Test API
          const apiResult = await testFeatureRequestAPI(testCase, useStream)
          results.push({
            test: `API ${useStream ? 'Stream' : 'JSON'} - Case ${i + 1}`,
            ...apiResult
          })

          // Small delay between tests
          await new Promise(resolve => setTimeout(resolve, 1000))
        }

        if (testAll || testService) {
          // Test service directly
          const serviceResult = await testServiceDirectly(testCase)
          results.push({
            test: `Service Direct - Case ${i + 1}`,
            ...serviceResult
          })
        }

      } catch (error) {
        console.error(`❌ Test case ${i + 1} failed:`, error.message)
        results.push({
          test: `Case ${i + 1}`,
          success: false,
          error: error.message,
          codeModifications: 0
        })
      }
    }

    // Summary
    console.log(`\n${'='.repeat(80)}`)
    console.log('📋 Integration Test Summary:')
    console.log('='.repeat(80))
    
    results.forEach(result => {
      const status = result.success ? '✅ PASSED' : '❌ FAILED'
      console.log(`  ${result.test}: ${status}`)
      if (result.codeModifications > 0) {
        console.log(`    💻 Code modifications: ${result.codeModifications}`)
      }
      if (result.error) {
        console.log(`    Error: ${result.error}`)
      }
    })

    const passedCount = results.filter(r => r.success).length
    const totalModifications = results.reduce((sum, r) => sum + (r.codeModifications || 0), 0)
    
    console.log(`\n📊 Overall Results:`)
    console.log(`  Tests: ${passedCount}/${results.length} passed`)
    console.log(`  Code modifications: ${totalModifications} total`)
    console.log(`\n${passedCount === results.length ? '🎉 ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)

    process.exit(passedCount === results.length ? 0 : 1)
  })().catch(error => {
    console.error('❌ Global test execution error:', error)
    process.exit(1)
  })
}

module.exports = { 
  testFeatureRequestAPI, 
  testServiceDirectly,
  TEST_FEATURE_REQUESTS
}
