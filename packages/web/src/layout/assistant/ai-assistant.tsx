"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { X, Send, ThumbsUp, ThumbsDown, Sparkles, MessageSquare, Lightbulb, Search, Code, FileText, Wand2, Play, GitBranch, CheckCircle, AlertCircle, Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"

interface AIAssistantProps {
  closeAssistant: () => void
  currentDocId: string | null
  isTablet: boolean
}

export function AIAssistant({ closeAssistant, isTablet }: AIAssistantProps) {
  const [messages, setMessages] = useState<Array<{ role: string; content: string }>>([
    {
      role: "assistant",
      content: "👋 你好！我是你的AI助手，可以帮助你查找文档、解答问题或提供代码示例。有什么我可以帮你的吗？",
    },
  ])
  const [input, setInput] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Feature Request states
  const [featureRequestInput, setFeatureRequestInput] = useState("")
  const [isProcessingFeature, setIsProcessingFeature] = useState(false)
  const [featureProgress, setFeatureProgress] = useState<{
    step: string
    status: 'pending' | 'running' | 'completed' | 'failed'
    details?: string
  }[]>([])
  const [featureResult, setFeatureResult] = useState<{
    success: boolean
    codeModifications: number
    summary?: string
    error?: string
  } | null>(null)

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [messages])

  const handleSend = () => {
    if (input.trim()) {
      // 添加用户消息
      setMessages((prev) => [...prev, { role: "user", content: input }])
      setInput("")

      // 模拟AI正在输入
      setIsTyping(true)

      // 模拟AI响应
      setTimeout(() => {
        setIsTyping(false)

        let response = ""
        if (input.toLowerCase().includes("oauth") || input.toLowerCase().includes("认证")) {
          response =
            "OAuth 2.0是一个授权框架，允许第三方应用获得对HTTP服务的有限访问权限。\n\n我们的API支持以下OAuth 2.0授权流程：\n\n1. 授权码流程（推荐）\n2. 客户端凭证流程\n3. 密码凭证流程\n\n你可以在[认证与授权](/guide-authentication)文档中找到详细的实现指南和示例代码。"
        } else if (input.toLowerCase().includes("产品") || input.toLowerCase().includes("api")) {
          response =
            "我们的产品API允许你管理产品目录，包括创建、更新、查询和删除产品。\n\n主要端点包括：\n\n- `GET /v1/products` - 获取产品列表\n- `GET /v1/products/{id}` - 获取单个产品\n- `POST /v1/products` - 创建产品\n- `PUT /v1/products/{id}` - 更新产品\n- `DELETE /v1/products/{id}` - 删除产品\n\n你需要查看具体哪个端点的文档吗？"
        } else if (input.toLowerCase().includes("错误") || input.toLowerCase().includes("问题")) {
          response =
            "常见的API错误状态码包括：\n\n- `401` - 认证错误，API密钥无效或已过期\n- `403` - 权限错误，没有访问资源的权限\n- `404` - 资源不存在\n- `422` - 参数验证错误\n- `429` - 请求过多，超出API速率限制\n\n如果你遇到特定的错误，可以提供错误代码和详细信息，我会帮你解决。"
        } else {
          response =
            "感谢你的问题！我可以帮你查找相关文档或提供更多信息。你可以问我关于API使用、认证方法、错误处理或任何技术问题。如果你需要特定的代码示例，也可以告诉我你使用的编程语言。"
        }

        setMessages((prev) => [...prev, { role: "assistant", content: response }])
      }, 1500)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  // Feature Request handlers
  const handleFeatureRequest = async () => {
    if (!featureRequestInput.trim() || isProcessingFeature) return

    setIsProcessingFeature(true)
    setFeatureResult(null)
    setFeatureProgress([])

    try {
      // Use Server-Sent Events for real-time progress
      const response = await fetch('/api/feature-request/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description: featureRequestInput,
          issueNumber: null, // Can be extracted from input if needed
        }),
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              if (data.type === 'progress') {
                setFeatureProgress(prev => {
                  const existing = prev.find(p => p.step === data.step)
                  if (existing) {
                    return prev.map(p =>
                      p.step === data.step
                        ? { ...p, status: data.status, details: data.details }
                        : p
                    )
                  } else {
                    return [...prev, {
                      step: data.step,
                      status: data.status,
                      details: data.details
                    }]
                  }
                })
              } else if (data.type === 'result') {
                setFeatureResult({
                  success: data.result.success,
                  codeModifications: data.result.codeModifications || 0,
                  summary: data.result.summary,
                })
              } else if (data.type === 'error') {
                throw new Error(data.error)
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', parseError)
            }
          }
        }
      }

    } catch (error) {
      console.error('Feature request failed:', error)
      setFeatureProgress(prev => prev.map(step => ({
        ...step,
        status: step.status === 'running' ? 'failed' : step.status
      })))

      setFeatureResult({
        success: false,
        codeModifications: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      })
    } finally {
      setIsProcessingFeature(false)
    }
  }

  const resetFeatureRequest = () => {
    setFeatureRequestInput("")
    setFeatureProgress([])
    setFeatureResult(null)
    setIsProcessingFeature(false)
  }

  return (
    <div
      className={cn(
        "fixed inset-y-0 right-0 z-30 bg-background border-l shadow-lg flex flex-col pt-16 transition-all duration-300",
        isTablet ? "w-80 md:w-96" : "w-full",
      )}
    >
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="font-semibold flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          AI助手
        </h2>
        <Button variant="ghost" size="icon" onClick={closeAssistant}>
          <X className="h-5 w-5" />
          <span className="sr-only">关闭</span>
        </Button>
      </div>

      <Tabs defaultValue="chat">
        <TabsList className="grid grid-cols-4 mx-4 my-2">
          <TabsTrigger value="chat" className="flex items-center gap-1">
            <MessageSquare className="h-4 w-4" />
            <span>对话</span>
          </TabsTrigger>
          <TabsTrigger value="suggestions" className="flex items-center gap-1">
            <Lightbulb className="h-4 w-4" />
            <span>建议</span>
          </TabsTrigger>
          <TabsTrigger value="search" className="flex items-center gap-1">
            <Search className="h-4 w-4" />
            <span>搜索</span>
          </TabsTrigger>
          <TabsTrigger value="feature" className="flex items-center gap-1">
            <Wand2 className="h-4 w-4" />
            <span>功能开发</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="m-0 flex-1 flex flex-col">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              {messages.map((message, index) => (
                <div key={index} className={cn("flex", message.role === "user" ? "justify-end" : "justify-start")}>
                  <div
                    className={cn(
                      "max-w-[80%] rounded-lg p-3",
                      message.role === "user" ? "bg-blue-600 text-white" : "bg-muted",
                    )}
                  >
                    <div className="whitespace-pre-wrap text-sm">{message.content}</div>

                    {message.role === "assistant" && (
                      <div className="flex items-center justify-end gap-1 mt-2">
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <ThumbsUp className="h-3 w-3" />
                          <span className="sr-only">有帮助</span>
                        </Button>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <ThumbsDown className="h-3 w-3" />
                          <span className="sr-only">没帮助</span>
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {isTyping && (
                <div className="flex justify-start">
                  <div className="max-w-[80%] rounded-lg p-3 bg-muted">
                    <div className="flex space-x-2">
                      <div className="h-2 w-2 rounded-full bg-blue-600 animate-bounce"></div>
                      <div className="h-2 w-2 rounded-full bg-blue-600 animate-bounce delay-75"></div>
                      <div className="h-2 w-2 rounded-full bg-blue-600 animate-bounce delay-150"></div>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          <div className="p-4 border-t">
            <div className="flex gap-2">
              <Textarea
                placeholder="输入你的问题..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                className="min-h-[80px] resize-none"
              />
              <Button className="self-end" onClick={handleSend} disabled={!input.trim() || isTyping}>
                <Send className="h-4 w-4" />
                <span className="sr-only">发送</span>
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-2 text-center">
              AI助手可能会产生不准确的信息。请验证重要内容。
            </p>
          </div>
        </TabsContent>

        <TabsContent value="suggestions" className="m-0 flex-1 flex flex-col">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              <div className="text-sm font-medium mb-2">推荐内容</div>

              <div className="border rounded-lg p-3 hover:bg-muted cursor-pointer transition-colors">
                <div className="flex items-start gap-2">
                  <Code className="h-4 w-4 mt-0.5 text-blue-600 dark:text-blue-400" />
                  <div>
                    <div className="font-medium text-sm">API认证指南</div>
                    <p className="text-xs text-muted-foreground mt-1">了解如何使用API密钥和OAuth 2.0进行认证</p>
                  </div>
                </div>
              </div>

              <div className="border rounded-lg p-3 hover:bg-muted cursor-pointer transition-colors">
                <div className="flex items-start gap-2">
                  <FileText className="h-4 w-4 mt-0.5 text-green-600 dark:text-green-400" />
                  <div>
                    <div className="font-medium text-sm">错误处理最佳实践</div>
                    <p className="text-xs text-muted-foreground mt-1">学习如何优雅地处理API错误和异常情况</p>
                  </div>
                </div>
              </div>

              <div className="border rounded-lg p-3 hover:bg-muted cursor-pointer transition-colors">
                <div className="flex items-start gap-2">
                  <Code className="h-4 w-4 mt-0.5 text-purple-600 dark:text-purple-400" />
                  <div>
                    <div className="font-medium text-sm">分页与筛选技巧</div>
                    <p className="text-xs text-muted-foreground mt-1">如何高效地使用API的分页和筛选功能</p>
                  </div>
                </div>
              </div>

              <div className="text-sm font-medium mt-6 mb-2">常见问题</div>

              <div className="space-y-2">
                <div
                  className="text-sm text-blue-600 dark:text-blue-400 hover:underline cursor-pointer"
                  onClick={() => {
                    setInput("如何使用OAuth 2.0认证？")
                    setMessages((prev) => [...prev, { role: "user", content: "如何使用OAuth 2.0认证？" }])

                    // 模拟AI正在输入
                    setIsTyping(true)

                    // 模拟AI响应
                    setTimeout(() => {
                      setIsTyping(false)
                      setMessages((prev) => [
                        ...prev,
                        {
                          role: "assistant",
                          content:
                            "OAuth 2.0是一个授权框架，允许第三方应用获得对HTTP服务的有限访问权限。\n\n我们的API支持以下OAuth 2.0授权流程：\n\n1. 授权码流程（推荐）\n2. 客户端凭证流程\n3. 密码凭证流程\n\n你可以在[认证与授权](/guide-authentication)文档中找到详细的实现指南和示例代码。",
                        },
                      ])
                    }, 1500)
                  }}
                >
                  如何使用OAuth 2.0认证？
                </div>
                <div
                  className="text-sm text-blue-600 dark:text-blue-400 hover:underline cursor-pointer"
                  onClick={() => {
                    setInput("API速率限制是多少？")
                    setMessages((prev) => [...prev, { role: "user", content: "API速率限制是多少？" }])

                    // 模拟AI正在输入
                    setIsTyping(true)

                    // 模拟AI响应
                    setTimeout(() => {
                      setIsTyping(false)
                      setMessages((prev) => [
                        ...prev,
                        {
                          role: "assistant",
                          content:
                            "我们的API速率限制根据您的账户类型而有所不同：\n\n- 免费账户：每分钟60个请求，每天1000个请求\n- 标准账户：每分钟300个请求，每天10,000个请求\n- 企业账户：每分钟3000个请求，每天无限制\n\n如果您超出限制，API将返回429状态码。您可以在响应头中查看剩余的请求配额：\n\n- X-RateLimit-Limit：当前时间窗口允许的请求数\n- X-RateLimit-Remaining：当前时间窗口剩余的请求数\n- X-RateLimit-Reset：重置计数器的时间（Unix时间戳）",
                        },
                      ])
                    }, 1500)
                  }}
                >
                  API速率限制是多少？
                </div>
                <div
                  className="text-sm text-blue-600 dark:text-blue-400 hover:underline cursor-pointer"
                  onClick={() => {
                    setInput("如何处理API错误？")
                    setMessages((prev) => [...prev, { role: "user", content: "如何处理API错误？" }])

                    // 模拟AI正在输入
                    setIsTyping(true)

                    // 模拟AI响应
                    setTimeout(() => {
                      setIsTyping(false)
                      setMessages((prev) => [
                        ...prev,
                        {
                          role: "assistant",
                          content:
                            '处理API错误的最佳实践：\n\n1. 始终检查HTTP状态码\n2. 解析错误响应中的详细信息\n3. 实现指数退避重试机制\n4. 记录错误详情以便调试\n\n我们的API在错误响应中返回统一的JSON格式：\n\n```json\n{\n  "error": {\n    "code": "invalid_request",\n    "message": "参数验证失败",\n    "details": [\n      {\n        "field": "email",\n        "message": "无效的邮箱格式"\n      }\n    ]\n  }\n}\n```\n\n您可以在[错误处理](/guide-error-handling)文档中找到更多信息和示例代码。',
                        },
                      ])
                    }, 1500)
                  }}
                >
                  如何处理API错误？
                </div>
              </div>
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="search" className="m-0 flex-1 flex flex-col">
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索文档..."
                className="w-full pl-8 pr-4 py-2 text-sm rounded-md border bg-background"
              />
            </div>
          </div>

          <ScrollArea className="flex-1 p-4">
            <div className="text-center py-12 text-muted-foreground">
              <p>输入关键词搜索文档</p>
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="feature" className="m-0 flex-1 flex flex-col">
          <div className="p-4 border-b">
            <div className="flex items-center gap-2 mb-2">
              <Wand2 className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium">AI 功能开发</span>
            </div>
            <p className="text-xs text-muted-foreground">
              描述您想要的功能，AI 将自动分析并生成代码实现
            </p>
          </div>

          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              {/* Input Section */}
              <div className="space-y-2">
                <label className="text-sm font-medium">功能描述</label>
                <Textarea
                  placeholder="请详细描述您想要实现的功能，例如：&#10;- 添加用户登录功能&#10;- 实现文件上传接口&#10;- 创建数据导出功能"
                  value={featureRequestInput}
                  onChange={(e) => setFeatureRequestInput(e.target.value)}
                  className="min-h-[100px] resize-none"
                  disabled={isProcessingFeature}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={handleFeatureRequest}
                  disabled={!featureRequestInput.trim() || isProcessingFeature}
                  className="flex-1"
                >
                  {isProcessingFeature ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      开发中...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      开始开发
                    </>
                  )}
                </Button>
                {(featureProgress.length > 0 || featureResult) && (
                  <Button
                    variant="outline"
                    onClick={resetFeatureRequest}
                    disabled={isProcessingFeature}
                  >
                    重置
                  </Button>
                )}
              </div>

              {/* Progress Section */}
              {featureProgress.length > 0 && (
                <div className="space-y-3">
                  <div className="text-sm font-medium">开发进度</div>
                  {featureProgress.map((step, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                      {step.status === 'running' && (
                        <Clock className="h-4 w-4 text-blue-600 animate-spin" />
                      )}
                      {step.status === 'completed' && (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      )}
                      {step.status === 'failed' && (
                        <AlertCircle className="h-4 w-4 text-red-600" />
                      )}
                      {step.status === 'pending' && (
                        <div className="h-4 w-4 border-2 border-gray-300 rounded-full" />
                      )}
                      <div className="flex-1">
                        <div className="text-sm font-medium">{step.step}</div>
                        {step.details && (
                          <div className="text-xs text-muted-foreground mt-1">{step.details}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Result Section */}
              {featureResult && (
                <div className="space-y-3">
                  <div className="text-sm font-medium">开发结果</div>
                  <div className={cn(
                    "p-4 border rounded-lg",
                    featureResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"
                  )}>
                    <div className="flex items-center gap-2 mb-2">
                      {featureResult.success ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm font-medium">
                        {featureResult.success ? "开发完成" : "开发失败"}
                      </span>
                    </div>

                    {featureResult.success && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <GitBranch className="h-4 w-4" />
                          <span>修改了 {featureResult.codeModifications} 个文件</span>
                        </div>
                        {featureResult.summary && (
                          <div className="text-sm text-muted-foreground">
                            {featureResult.summary}
                          </div>
                        )}
                      </div>
                    )}

                    {featureResult.error && (
                      <div className="text-sm text-red-600">
                        错误: {featureResult.error}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Help Section */}
              <div className="mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="text-sm font-medium text-blue-800 mb-1">💡 使用提示</div>
                <div className="text-xs text-blue-700 space-y-1">
                  <p>• 详细描述功能需求，包括具体的业务场景</p>
                  <p>• 可以参考现有的 GitHub Issue 编号</p>
                  <p>• AI 会自动分析代码库并生成实现方案</p>
                  <p>• 生成的代码会自动保存到相应文件中</p>
                </div>
              </div>
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  )
}
