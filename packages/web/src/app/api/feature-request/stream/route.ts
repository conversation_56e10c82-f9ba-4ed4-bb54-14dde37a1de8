import { NextRequest } from 'next/server'
import { join } from 'path'

interface FeatureRequestBody {
  description: string
  issueNumber?: number
  owner?: string
  repo?: string
}

interface ProgressUpdate {
  type: 'progress' | 'result' | 'error'
  step?: string
  status?: 'pending' | 'running' | 'completed' | 'failed'
  details?: string
  result?: any
  error?: string
}

export async function POST(request: NextRequest) {
  const body: FeatureRequestBody = await request.json()
  
  if (!body.description?.trim()) {
    return new Response('Description is required', { status: 400 })
  }

  // Create a readable stream for Server-Sent Events
  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder()
      
      const sendUpdate = (update: ProgressUpdate) => {
        const data = `data: ${JSON.stringify(update)}\n\n`
        controller.enqueue(encoder.encode(data))
      }

      try {
        // Send initial progress
        sendUpdate({
          type: 'progress',
          step: '初始化开发环境',
          status: 'running',
          details: '正在加载 AI 代理和工具...'
        })

        // Import the FeatureRequestService dynamically
        const { FeatureRequestService } = await import('../../../../../remote-agent/dist/services/feature-request-service.js')

        sendUpdate({
          type: 'progress',
          step: '初始化开发环境',
          status: 'completed',
          details: 'AI 代理已就绪'
        })

        sendUpdate({
          type: 'progress',
          step: '分析功能需求',
          status: 'running',
          details: '正在分析功能描述和需求...'
        })

        // Create service instance with progress callback
        const service = new FeatureRequestService({
          description: body.description,
          issueNumber: body.issueNumber,
          owner: body.owner,
          repo: body.repo,
          workspacePath: join(process.cwd(), '../../'),
          githubToken: process.env.GITHUB_TOKEN,
          verbose: false,
          maxToolRounds: 8,
          validateCodeChanges: true
        })

        // Simulate progress updates during execution
        const progressSteps = [
          { step: '分析功能需求', delay: 1000 },
          { step: '搜索相关代码', delay: 2000 },
          { step: '生成实现方案', delay: 3000 },
          { step: '修改代码文件', delay: 2000 }
        ]

        let currentStepIndex = 0

        // Start the actual feature implementation
        const implementationPromise = service.implementFeature()

        // Send progress updates
        const progressInterval = setInterval(() => {
          if (currentStepIndex < progressSteps.length) {
            const currentStep = progressSteps[currentStepIndex]
            
            if (currentStepIndex > 0) {
              // Mark previous step as completed
              sendUpdate({
                type: 'progress',
                step: progressSteps[currentStepIndex - 1].step,
                status: 'completed'
              })
            }

            // Start current step
            sendUpdate({
              type: 'progress',
              step: currentStep.step,
              status: 'running',
              details: `正在执行 ${currentStep.step}...`
            })

            currentStepIndex++
          } else {
            clearInterval(progressInterval)
          }
        }, 2000)

        // Wait for implementation to complete
        const result = await implementationPromise
        clearInterval(progressInterval)

        // Mark final step as completed
        if (progressSteps.length > 0) {
          sendUpdate({
            type: 'progress',
            step: progressSteps[progressSteps.length - 1].step,
            status: result.success ? 'completed' : 'failed'
          })
        }

        // Send final result
        sendUpdate({
          type: 'result',
          result: {
            success: result.success,
            codeModifications: result.codeModifications,
            summary: result.summary,
            error: result.error,
            executionTime: result.executionTime,
            toolsUsed: result.toolsUsed
          }
        })

      } catch (error) {
        console.error('Feature request stream error:', error)
        
        sendUpdate({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      } finally {
        controller.close()
      }
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

export async function GET() {
  return new Response(JSON.stringify({
    message: 'Feature Request Stream API',
    description: 'Use POST to start a feature request with real-time progress updates via Server-Sent Events'
  }), {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
