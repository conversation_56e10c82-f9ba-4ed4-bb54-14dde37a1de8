import { NextRequest, NextResponse } from 'next/server'
import { join } from 'path'

interface FeatureRequestBody {
  description: string
  issueNumber?: number
  owner?: string
  repo?: string
}

interface FeatureRequestResult {
  success: boolean
  codeModifications: number
  summary?: string
  error?: string
  toolResults?: any[]
  totalRounds?: number
  executionTime?: number
}

export async function POST(request: NextRequest) {
  try {
    const body: FeatureRequestBody = await request.json()

    if (!body.description?.trim()) {
      return NextResponse.json(
        { error: 'Description is required' },
        { status: 400 }
      )
    }

    // Import the FeatureRequestService dynamically to avoid build issues
    const { FeatureRequestService } = await import('../../../../remote-agent/dist/services/feature-request-service.js')

    // Create service instance
    const service = new FeatureRequestService({
      description: body.description,
      issueNumber: body.issueNumber,
      owner: body.owner,
      repo: body.repo,
      workspacePath: join(process.cwd(), '../../'),
      githubToken: process.env.GITHUB_TOKEN,
      verbose: false, // Disable verbose logging in API
      maxToolRounds: 8,
      validateCodeChanges: true
    })

    // Execute the feature request
    const result = await service.implementFeature()

    // Transform result for API response
    const apiResult: FeatureRequestResult = {
      success: result.success,
      codeModifications: result.codeModifications,
      summary: result.summary,
      error: result.error,
      toolResults: result.response.toolResults,
      totalRounds: result.response.totalRounds,
      executionTime: result.executionTime
    }

    return NextResponse.json(apiResult)

  } catch (error) {
    console.error('Feature request API error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Helper function to extract summary from response text
function extractSummary(text: string): string {
  // Try to extract a meaningful summary from the response
  const lines = text.split('\n').filter(line => line.trim())
  
  // Look for summary sections
  const summaryPatterns = [
    /(?:summary|概述|总结)[:\s]*([^#\n]+)/i,
    /(?:implementation|实现)[:\s]*([^#\n]+)/i,
    /(?:result|结果)[:\s]*([^#\n]+)/i
  ]
  
  for (const pattern of summaryPatterns) {
    const match = text.match(pattern)
    if (match && match[1]) {
      return match[1].trim().substring(0, 200) + (match[1].length > 200 ? '...' : '')
    }
  }
  
  // Fallback: use first meaningful paragraph
  const meaningfulLines = lines.filter(line => 
    line.length > 20 && 
    !line.startsWith('#') && 
    !line.startsWith('```')
  )
  
  if (meaningfulLines.length > 0) {
    return meaningfulLines[0].substring(0, 200) + (meaningfulLines[0].length > 200 ? '...' : '')
  }
  
  return '功能开发完成，请查看详细日志了解具体实现。'
}

export async function GET() {
  return NextResponse.json({
    message: 'Feature Request API',
    endpoints: {
      'POST /api/feature-request': 'Submit a feature request for automated development'
    }
  })
}
