{"name": "@autodev/context-mcp", "version": "0.7.1", "description": "Model Context Protocol implementation for AutoDev", "main": "dist/index.js", "bin": {"autodev-context-mcp": "./bin/index.js"}, "module": "dist/index.esm.js", "type": "commonjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist", "bin", "example", "package.json", ".env.example", "README.md"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "run-example": "pnpm run build && npx tsx example/server.ts", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "pnpm run build"}, "keywords": ["mcp", "model-context-protocol", "typescript"], "author": "AutoDev authors", "license": "MIT", "dependencies": {"@ast-grep/lang-typescript": "^0.0.2", "@ast-grep/napi": "^0.38.1", "@gitbeaker/rest": "^42.5.0", "@modelcontextprotocol/sdk": "^1.11.1", "@octokit/rest": "^20.1.2", "@vscode/ripgrep": "^1.15.11", "express": "^5.1.0", "zod": "^3.24.4"}, "devDependencies": {"@jest/globals": "^29.7.0", "@octokit/types": "^14.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@types/express": "^5.0.1", "@types/jest": "^29.5.11", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "eslint": "^8.57.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "rollup": "^4.9.1", "rollup-plugin-dts": "^6.1.0", "tree-sitter-cli": "^0.25.4", "ts-jest": "^29.1.1", "typescript": "^5.3.3", "@types/diff": "^5.0.3", "rollup-plugin-typescript2": "^0.36.0", "tsx": "^4.7.0"}, "publishConfig": {"access": "public"}}